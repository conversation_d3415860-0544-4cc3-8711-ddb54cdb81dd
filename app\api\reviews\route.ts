import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import Review, { CreateReviewData } from '@/models/Review'

// GET - Fetch all approved reviews
export async function GET() {
  try {
    await connectDB()

    const reviews = await Review
      .find({ isApproved: true })
      .sort({ createdAt: -1 })
      .lean() // Returns plain JavaScript objects instead of Mongoose documents

    return NextResponse.json({
      success: true,
      reviews: reviews.map(review => ({
        ...review,
        id: review._id?.toString(),
        _id: undefined
      }))
    })
  } catch (error) {
    console.error('Error fetching reviews:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch reviews' },
      { status: 500 }
    )
  }
}

// POST - Create a new review
export async function POST(request: NextRequest) {
  try {
    await connectDB()

    const body: CreateReviewData = await request.json()

    // Create the review with Mongoose (validation happens automatically)
    const now = new Date()

    const newReview = new Review({
      name: body.name,
      title: body.title,
      rating: body.rating,
      date: now.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      review: body.review,
      fullReview: body.review,
      email: body.email,
      isApproved: true // Auto-approve for now
    })

    // Save to database (Mongoose handles validation)
    const savedReview = await newReview.save()

    const createdReview = {
      ...savedReview.toObject(),
      id: savedReview._id.toString(),
      _id: undefined
    }

    return NextResponse.json({
      success: true,
      review: createdReview,
      message: 'Review created successfully'
    })
  } catch (error: any) {
    console.error('Error creating review:', error)

    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message)
      return NextResponse.json(
        { success: false, error: `Validation error: ${validationErrors.join(', ')}` },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create review' },
      { status: 500 }
    )
  }
}
