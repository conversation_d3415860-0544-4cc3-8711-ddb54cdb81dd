(()=>{"use strict";var e={333:(e,t,r)=>{const n=r(137);const u=r(179);const s=r(13);const o=r(719);const braces=(e,t={})=>{let r=[];if(Array.isArray(e)){for(let n of e){let e=braces.create(n,t);if(Array.isArray(e)){r.push(...e)}else{r.push(e)}}}else{r=[].concat(braces.create(e,t))}if(t&&t.expand===true&&t.nodupes===true){r=[...new Set(r)]}return r};braces.parse=(e,t={})=>o(e,t);braces.stringify=(e,t={})=>{if(typeof e==="string"){return n(braces.parse(e,t),t)}return n(e,t)};braces.compile=(e,t={})=>{if(typeof e==="string"){e=braces.parse(e,t)}return u(e,t)};braces.expand=(e,t={})=>{if(typeof e==="string"){e=braces.parse(e,t)}let r=s(e,t);if(t.noempty===true){r=r.filter(Boolean)}if(t.nodupes===true){r=[...new Set(r)]}return r};braces.create=(e,t={})=>{if(e===""||e.length<3){return[e]}return t.expand!==true?braces.compile(e,t):braces.expand(e,t)};e.exports=braces},179:(e,t,r)=>{const n=r(783);const u=r(617);const compile=(e,t={})=>{let walk=(e,r={})=>{let s=u.isInvalidBrace(r);let o=e.invalid===true&&t.escapeInvalid===true;let i=s===true||o===true;let a=t.escapeInvalid===true?"\\":"";let l="";if(e.isOpen===true){return a+e.value}if(e.isClose===true){return a+e.value}if(e.type==="open"){return i?a+e.value:"("}if(e.type==="close"){return i?a+e.value:")"}if(e.type==="comma"){return e.prev.type==="comma"?"":i?e.value:"|"}if(e.value){return e.value}if(e.nodes&&e.ranges>0){let r=u.reduce(e.nodes);let s=n(...r,{...t,wrap:false,toRegex:true});if(s.length!==0){return r.length>1&&s.length>1?`(${s})`:s}}if(e.nodes){for(let t of e.nodes){l+=walk(t,e)}}return l};return walk(e)};e.exports=compile},457:e=>{e.exports={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:"\n",CHAR_NO_BREAK_SPACE:" ",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"\t",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\ufeff"}},13:(e,t,r)=>{const n=r(783);const u=r(137);const s=r(617);const append=(e="",t="",r=false)=>{let n=[];e=[].concat(e);t=[].concat(t);if(!t.length)return e;if(!e.length){return r?s.flatten(t).map((e=>`{${e}}`)):t}for(let u of e){if(Array.isArray(u)){for(let e of u){n.push(append(e,t,r))}}else{for(let e of t){if(r===true&&typeof e==="string")e=`{${e}}`;n.push(Array.isArray(e)?append(u,e,r):u+e)}}}return s.flatten(n)};const expand=(e,t={})=>{let r=t.rangeLimit===void 0?1e3:t.rangeLimit;let walk=(e,o={})=>{e.queue=[];let i=o;let a=o.queue;while(i.type!=="brace"&&i.type!=="root"&&i.parent){i=i.parent;a=i.queue}if(e.invalid||e.dollar){a.push(append(a.pop(),u(e,t)));return}if(e.type==="brace"&&e.invalid!==true&&e.nodes.length===2){a.push(append(a.pop(),["{}"]));return}if(e.nodes&&e.ranges>0){let o=s.reduce(e.nodes);if(s.exceedsLimit(...o,t.step,r)){throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.")}let i=n(...o,t);if(i.length===0){i=u(e,t)}a.push(append(a.pop(),i));e.nodes=[];return}let l=s.encloseBrace(e);let c=e.queue;let p=e;while(p.type!=="brace"&&p.type!=="root"&&p.parent){p=p.parent;c=p.queue}for(let t=0;t<e.nodes.length;t++){let r=e.nodes[t];if(r.type==="comma"&&e.type==="brace"){if(t===1)c.push("");c.push("");continue}if(r.type==="close"){a.push(append(a.pop(),c,l));continue}if(r.value&&r.type!=="open"){c.push(append(c.pop(),r.value));continue}if(r.nodes){walk(r,e)}}return c};return s.flatten(walk(e))};e.exports=expand},719:(e,t,r)=>{const n=r(137);const{MAX_LENGTH:u,CHAR_BACKSLASH:s,CHAR_BACKTICK:o,CHAR_COMMA:i,CHAR_DOT:a,CHAR_LEFT_PARENTHESES:l,CHAR_RIGHT_PARENTHESES:c,CHAR_LEFT_CURLY_BRACE:p,CHAR_RIGHT_CURLY_BRACE:f,CHAR_LEFT_SQUARE_BRACKET:A,CHAR_RIGHT_SQUARE_BRACKET:R,CHAR_DOUBLE_QUOTE:_,CHAR_SINGLE_QUOTE:h,CHAR_NO_BREAK_SPACE:g,CHAR_ZERO_WIDTH_NOBREAK_SPACE:E}=r(457);const parse=(e,t={})=>{if(typeof e!=="string"){throw new TypeError("Expected a string")}let r=t||{};let C=typeof r.maxLength==="number"?Math.min(u,r.maxLength):u;if(e.length>C){throw new SyntaxError(`Input length (${e.length}), exceeds max characters (${C})`)}let y={type:"root",input:e,nodes:[]};let d=[y];let x=y;let b=y;let S=0;let H=e.length;let v=0;let $=0;let m;let T={};const advance=()=>e[v++];const push=e=>{if(e.type==="text"&&b.type==="dot"){b.type="text"}if(b&&b.type==="text"&&e.type==="text"){b.value+=e.value;return}x.nodes.push(e);e.parent=x;e.prev=b;b=e;return e};push({type:"bos"});while(v<H){x=d[d.length-1];m=advance();if(m===E||m===g){continue}if(m===s){push({type:"text",value:(t.keepEscaping?m:"")+advance()});continue}if(m===R){push({type:"text",value:"\\"+m});continue}if(m===A){S++;let e=true;let t;while(v<H&&(t=advance())){m+=t;if(t===A){S++;continue}if(t===s){m+=advance();continue}if(t===R){S--;if(S===0){break}}}push({type:"text",value:m});continue}if(m===l){x=push({type:"paren",nodes:[]});d.push(x);push({type:"text",value:m});continue}if(m===c){if(x.type!=="paren"){push({type:"text",value:m});continue}x=d.pop();push({type:"text",value:m});x=d[d.length-1];continue}if(m===_||m===h||m===o){let e=m;let r;if(t.keepQuotes!==true){m=""}while(v<H&&(r=advance())){if(r===s){m+=r+advance();continue}if(r===e){if(t.keepQuotes===true)m+=r;break}m+=r}push({type:"text",value:m});continue}if(m===p){$++;let e=b.value&&b.value.slice(-1)==="$"||x.dollar===true;let t={type:"brace",open:true,close:false,dollar:e,depth:$,commas:0,ranges:0,nodes:[]};x=push(t);d.push(x);push({type:"open",value:m});continue}if(m===f){if(x.type!=="brace"){push({type:"text",value:m});continue}let e="close";x=d.pop();x.close=true;push({type:e,value:m});$--;x=d[d.length-1];continue}if(m===i&&$>0){if(x.ranges>0){x.ranges=0;let e=x.nodes.shift();x.nodes=[e,{type:"text",value:n(x)}]}push({type:"comma",value:m});x.commas++;continue}if(m===a&&$>0&&x.commas===0){let e=x.nodes;if($===0||e.length===0){push({type:"text",value:m});continue}if(b.type==="dot"){x.range=[];b.value+=m;b.type="range";if(x.nodes.length!==3&&x.nodes.length!==5){x.invalid=true;x.ranges=0;b.type="text";continue}x.ranges++;x.args=[];continue}if(b.type==="range"){e.pop();let t=e[e.length-1];t.value+=b.value+m;b=t;x.ranges--;continue}push({type:"dot",value:m});continue}push({type:"text",value:m})}do{x=d.pop();if(x.type!=="root"){x.nodes.forEach((e=>{if(!e.nodes){if(e.type==="open")e.isOpen=true;if(e.type==="close")e.isClose=true;if(!e.nodes)e.type="text";e.invalid=true}}));let e=d[d.length-1];let t=e.nodes.indexOf(x);e.nodes.splice(t,1,...x.nodes)}}while(d.length>0);push({type:"eos"});return y};e.exports=parse},137:(e,t,r)=>{const n=r(617);e.exports=(e,t={})=>{let stringify=(e,r={})=>{let u=t.escapeInvalid&&n.isInvalidBrace(r);let s=e.invalid===true&&t.escapeInvalid===true;let o="";if(e.value){if((u||s)&&n.isOpenOrClose(e)){return"\\"+e.value}return e.value}if(e.value){return e.value}if(e.nodes){for(let t of e.nodes){o+=stringify(t)}}return o};return stringify(e)}},617:(e,t)=>{t.isInteger=e=>{if(typeof e==="number"){return Number.isInteger(e)}if(typeof e==="string"&&e.trim()!==""){return Number.isInteger(Number(e))}return false};t.find=(e,t)=>e.nodes.find((e=>e.type===t));t.exceedsLimit=(e,r,n=1,u)=>{if(u===false)return false;if(!t.isInteger(e)||!t.isInteger(r))return false;return(Number(r)-Number(e))/Number(n)>=u};t.escapeNode=(e,t=0,r)=>{let n=e.nodes[t];if(!n)return;if(r&&n.type===r||n.type==="open"||n.type==="close"){if(n.escaped!==true){n.value="\\"+n.value;n.escaped=true}}};t.encloseBrace=e=>{if(e.type!=="brace")return false;if(e.commas>>0+e.ranges>>0===0){e.invalid=true;return true}return false};t.isInvalidBrace=e=>{if(e.type!=="brace")return false;if(e.invalid===true||e.dollar)return true;if(e.commas>>0+e.ranges>>0===0){e.invalid=true;return true}if(e.open!==true||e.close!==true){e.invalid=true;return true}return false};t.isOpenOrClose=e=>{if(e.type==="open"||e.type==="close"){return true}return e.open===true||e.close===true};t.reduce=e=>e.reduce(((e,t)=>{if(t.type==="text")e.push(t.value);if(t.type==="range")t.type="text";return e}),[]);t.flatten=(...e)=>{const t=[];const flat=e=>{for(let r=0;r<e.length;r++){let n=e[r];Array.isArray(n)?flat(n,t):n!==void 0&&t.push(n)}return t};flat(e);return t}},783:(e,t,r)=>{
/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */
const n=r(837);const u=r(492);const isObject=e=>e!==null&&typeof e==="object"&&!Array.isArray(e);const transform=e=>t=>e===true?Number(t):String(t);const isValidValue=e=>typeof e==="number"||typeof e==="string"&&e!=="";const isNumber=e=>Number.isInteger(+e);const zeros=e=>{let t=`${e}`;let r=-1;if(t[0]==="-")t=t.slice(1);if(t==="0")return false;while(t[++r]==="0");return r>0};const stringify=(e,t,r)=>{if(typeof e==="string"||typeof t==="string"){return true}return r.stringify===true};const pad=(e,t,r)=>{if(t>0){let r=e[0]==="-"?"-":"";if(r)e=e.slice(1);e=r+e.padStart(r?t-1:t,"0")}if(r===false){return String(e)}return e};const toMaxLen=(e,t)=>{let r=e[0]==="-"?"-":"";if(r){e=e.slice(1);t--}while(e.length<t)e="0"+e;return r?"-"+e:e};const toSequence=(e,t)=>{e.negatives.sort(((e,t)=>e<t?-1:e>t?1:0));e.positives.sort(((e,t)=>e<t?-1:e>t?1:0));let r=t.capture?"":"?:";let n="";let u="";let s;if(e.positives.length){n=e.positives.join("|")}if(e.negatives.length){u=`-(${r}${e.negatives.join("|")})`}if(n&&u){s=`${n}|${u}`}else{s=n||u}if(t.wrap){return`(${r}${s})`}return s};const toRange=(e,t,r,n)=>{if(r){return u(e,t,{wrap:false,...n})}let s=String.fromCharCode(e);if(e===t)return s;let o=String.fromCharCode(t);return`[${s}-${o}]`};const toRegex=(e,t,r)=>{if(Array.isArray(e)){let t=r.wrap===true;let n=r.capture?"":"?:";return t?`(${n}${e.join("|")})`:e.join("|")}return u(e,t,r)};const rangeError=(...e)=>new RangeError("Invalid range arguments: "+n.inspect(...e));const invalidRange=(e,t,r)=>{if(r.strictRanges===true)throw rangeError([e,t]);return[]};const invalidStep=(e,t)=>{if(t.strictRanges===true){throw new TypeError(`Expected step "${e}" to be a number`)}return[]};const fillNumbers=(e,t,r=1,n={})=>{let u=Number(e);let s=Number(t);if(!Number.isInteger(u)||!Number.isInteger(s)){if(n.strictRanges===true)throw rangeError([e,t]);return[]}if(u===0)u=0;if(s===0)s=0;let o=u>s;let i=String(e);let a=String(t);let l=String(r);r=Math.max(Math.abs(r),1);let c=zeros(i)||zeros(a)||zeros(l);let p=c?Math.max(i.length,a.length,l.length):0;let f=c===false&&stringify(e,t,n)===false;let A=n.transform||transform(f);if(n.toRegex&&r===1){return toRange(toMaxLen(e,p),toMaxLen(t,p),true,n)}let R={negatives:[],positives:[]};let push=e=>R[e<0?"negatives":"positives"].push(Math.abs(e));let _=[];let h=0;while(o?u>=s:u<=s){if(n.toRegex===true&&r>1){push(u)}else{_.push(pad(A(u,h),p,f))}u=o?u-r:u+r;h++}if(n.toRegex===true){return r>1?toSequence(R,n):toRegex(_,null,{wrap:false,...n})}return _};const fillLetters=(e,t,r=1,n={})=>{if(!isNumber(e)&&e.length>1||!isNumber(t)&&t.length>1){return invalidRange(e,t,n)}let u=n.transform||(e=>String.fromCharCode(e));let s=`${e}`.charCodeAt(0);let o=`${t}`.charCodeAt(0);let i=s>o;let a=Math.min(s,o);let l=Math.max(s,o);if(n.toRegex&&r===1){return toRange(a,l,false,n)}let c=[];let p=0;while(i?s>=o:s<=o){c.push(u(s,p));s=i?s-r:s+r;p++}if(n.toRegex===true){return toRegex(c,null,{wrap:false,options:n})}return c};const fill=(e,t,r,n={})=>{if(t==null&&isValidValue(e)){return[e]}if(!isValidValue(e)||!isValidValue(t)){return invalidRange(e,t,n)}if(typeof r==="function"){return fill(e,t,1,{transform:r})}if(isObject(r)){return fill(e,t,0,r)}let u={...n};if(u.capture===true)u.wrap=true;r=r||u.step||1;if(!isNumber(r)){if(r!=null&&!isObject(r))return invalidStep(r,u);return fill(e,t,1,r)}if(isNumber(e)&&isNumber(t)){return fillNumbers(e,t,r,u)}return fillLetters(e,t,Math.max(Math.abs(r),1),u)};e.exports=fill},357:e=>{
/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */
e.exports=function(e){if(typeof e==="number"){return e-e===0}if(typeof e==="string"&&e.trim()!==""){return Number.isFinite?Number.isFinite(+e):isFinite(+e)}return false}},971:(e,t,r)=>{const n=r(837);const u=r(333);const s=r(251);const o=r(513);const isEmptyString=e=>e===""||e==="./";const micromatch=(e,t,r)=>{t=[].concat(t);e=[].concat(e);let n=new Set;let u=new Set;let o=new Set;let i=0;let onResult=e=>{o.add(e.output);if(r&&r.onResult){r.onResult(e)}};for(let o=0;o<t.length;o++){let a=s(String(t[o]),{...r,onResult:onResult},true);let l=a.state.negated||a.state.negatedExtglob;if(l)i++;for(let t of e){let e=a(t,true);let r=l?!e.isMatch:e.isMatch;if(!r)continue;if(l){n.add(e.output)}else{n.delete(e.output);u.add(e.output)}}}let a=i===t.length?[...o]:[...u];let l=a.filter((e=>!n.has(e)));if(r&&l.length===0){if(r.failglob===true){throw new Error(`No matches found for "${t.join(", ")}"`)}if(r.nonull===true||r.nullglob===true){return r.unescape?t.map((e=>e.replace(/\\/g,""))):t}}return l};micromatch.match=micromatch;micromatch.matcher=(e,t)=>s(e,t);micromatch.isMatch=(e,t,r)=>s(t,r)(e);micromatch.any=micromatch.isMatch;micromatch.not=(e,t,r={})=>{t=[].concat(t).map(String);let n=new Set;let u=[];let onResult=e=>{if(r.onResult)r.onResult(e);u.push(e.output)};let s=micromatch(e,t,{...r,onResult:onResult});for(let e of u){if(!s.includes(e)){n.add(e)}}return[...n]};micromatch.contains=(e,t,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected a string: "${n.inspect(e)}"`)}if(Array.isArray(t)){return t.some((t=>micromatch.contains(e,t,r)))}if(typeof t==="string"){if(isEmptyString(e)||isEmptyString(t)){return false}if(e.includes(t)||e.startsWith("./")&&e.slice(2).includes(t)){return true}}return micromatch.isMatch(e,t,{...r,contains:true})};micromatch.matchKeys=(e,t,r)=>{if(!o.isObject(e)){throw new TypeError("Expected the first argument to be an object")}let n=micromatch(Object.keys(e),t,r);let u={};for(let t of n)u[t]=e[t];return u};micromatch.some=(e,t,r)=>{let n=[].concat(e);for(let e of[].concat(t)){let t=s(String(e),r);if(n.some((e=>t(e)))){return true}}return false};micromatch.every=(e,t,r)=>{let n=[].concat(e);for(let e of[].concat(t)){let t=s(String(e),r);if(!n.every((e=>t(e)))){return false}}return true};micromatch.all=(e,t,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected a string: "${n.inspect(e)}"`)}return[].concat(t).every((t=>s(t,r)(e)))};micromatch.capture=(e,t,r)=>{let n=o.isWindows(r);let u=s.makeRe(String(e),{...r,capture:true});let i=u.exec(n?o.toPosixSlashes(t):t);if(i){return i.slice(1).map((e=>e===void 0?"":e))}};micromatch.makeRe=(...e)=>s.makeRe(...e);micromatch.scan=(...e)=>s.scan(...e);micromatch.parse=(e,t)=>{let r=[];for(let n of[].concat(e||[])){for(let e of u(String(n),t)){r.push(s.parse(e,t))}}return r};micromatch.braces=(e,t)=>{if(typeof e!=="string")throw new TypeError("Expected a string");if(t&&t.nobrace===true||!/\{.*\}/.test(e)){return[e]}return u(e,t)};micromatch.braceExpand=(e,t)=>{if(typeof e!=="string")throw new TypeError("Expected a string");return micromatch.braces(e,{...t,expand:true})};e.exports=micromatch},251:(e,t,r)=>{e.exports=r(683)},356:(e,t,r)=>{const n=r(17);const u="\\\\/";const s=`[^${u}]`;const o="\\.";const i="\\+";const a="\\?";const l="\\/";const c="(?=.)";const p="[^/]";const f=`(?:${l}|$)`;const A=`(?:^|${l})`;const R=`${o}{1,2}${f}`;const _=`(?!${o})`;const h=`(?!${A}${R})`;const g=`(?!${o}{0,1}${f})`;const E=`(?!${R})`;const C=`[^.${l}]`;const y=`${p}*?`;const d={DOT_LITERAL:o,PLUS_LITERAL:i,QMARK_LITERAL:a,SLASH_LITERAL:l,ONE_CHAR:c,QMARK:p,END_ANCHOR:f,DOTS_SLASH:R,NO_DOT:_,NO_DOTS:h,NO_DOT_SLASH:g,NO_DOTS_SLASH:E,QMARK_NO_DOT:C,STAR:y,START_ANCHOR:A};const x={...d,SLASH_LITERAL:`[${u}]`,QMARK:s,STAR:`${s}*?`,DOTS_SLASH:`${o}{1,2}(?:[${u}]|$)`,NO_DOT:`(?!${o})`,NO_DOTS:`(?!(?:^|[${u}])${o}{1,2}(?:[${u}]|$))`,NO_DOT_SLASH:`(?!${o}{0,1}(?:[${u}]|$))`,NO_DOTS_SLASH:`(?!${o}{1,2}(?:[${u}]|$))`,QMARK_NO_DOT:`[^.${u}]`,START_ANCHOR:`(?:^|[${u}])`,END_ANCHOR:`(?:[${u}]|$)`};const b={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};e.exports={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:b,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:n.sep,extglobChars(e){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${e.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(e){return e===true?x:d}}},754:(e,t,r)=>{const n=r(356);const u=r(513);const{MAX_LENGTH:s,POSIX_REGEX_SOURCE:o,REGEX_NON_SPECIAL_CHARS:i,REGEX_SPECIAL_CHARS_BACKREF:a,REPLACEMENTS:l}=n;const expandRange=(e,t)=>{if(typeof t.expandRange==="function"){return t.expandRange(...e,t)}e.sort();const r=`[${e.join("-")}]`;try{new RegExp(r)}catch(t){return e.map((e=>u.escapeRegex(e))).join("..")}return r};const syntaxError=(e,t)=>`Missing ${e}: "${t}" - use "\\\\${t}" to match literal characters`;const parse=(e,t)=>{if(typeof e!=="string"){throw new TypeError("Expected a string")}e=l[e]||e;const r={...t};const c=typeof r.maxLength==="number"?Math.min(s,r.maxLength):s;let p=e.length;if(p>c){throw new SyntaxError(`Input length: ${p}, exceeds maximum allowed length: ${c}`)}const f={type:"bos",value:"",output:r.prepend||""};const A=[f];const R=r.capture?"":"?:";const _=u.isWindows(t);const h=n.globChars(_);const g=n.extglobChars(h);const{DOT_LITERAL:E,PLUS_LITERAL:C,SLASH_LITERAL:y,ONE_CHAR:d,DOTS_SLASH:x,NO_DOT:b,NO_DOT_SLASH:S,NO_DOTS_SLASH:H,QMARK:v,QMARK_NO_DOT:$,STAR:m,START_ANCHOR:T}=h;const globstar=e=>`(${R}(?:(?!${T}${e.dot?x:E}).)*?)`;const L=r.dot?"":b;const O=r.dot?v:$;let w=r.bash===true?globstar(r):m;if(r.capture){w=`(${w})`}if(typeof r.noext==="boolean"){r.noextglob=r.noext}const N={input:e,index:-1,start:0,dot:r.dot===true,consumed:"",output:"",prefix:"",backtrack:false,negated:false,brackets:0,braces:0,parens:0,quotes:0,globstar:false,tokens:A};e=u.removePrefix(e,N);p=e.length;const k=[];const I=[];const M=[];let P=f;let B;const eos=()=>N.index===p-1;const G=N.peek=(t=1)=>e[N.index+t];const D=N.advance=()=>e[++N.index];const remaining=()=>e.slice(N.index+1);const consume=(e="",t=0)=>{N.consumed+=e;N.index+=t};const append=e=>{N.output+=e.output!=null?e.output:e.value;consume(e.value)};const negate=()=>{let e=1;while(G()==="!"&&(G(2)!=="("||G(3)==="?")){D();N.start++;e++}if(e%2===0){return false}N.negated=true;N.start++;return true};const increment=e=>{N[e]++;M.push(e)};const decrement=e=>{N[e]--;M.pop()};const push=e=>{if(P.type==="globstar"){const t=N.braces>0&&(e.type==="comma"||e.type==="brace");const r=e.extglob===true||k.length&&(e.type==="pipe"||e.type==="paren");if(e.type!=="slash"&&e.type!=="paren"&&!t&&!r){N.output=N.output.slice(0,-P.output.length);P.type="star";P.value="*";P.output=w;N.output+=P.output}}if(k.length&&e.type!=="paren"&&!g[e.value]){k[k.length-1].inner+=e.value}if(e.value||e.output)append(e);if(P&&P.type==="text"&&e.type==="text"){P.value+=e.value;P.output=(P.output||"")+e.value;return}e.prev=P;A.push(e);P=e};const extglobOpen=(e,t)=>{const n={...g[t],conditions:1,inner:""};n.prev=P;n.parens=N.parens;n.output=N.output;const u=(r.capture?"(":"")+n.open;increment("parens");push({type:e,value:t,output:N.output?"":d});push({type:"paren",extglob:true,value:D(),output:u});k.push(n)};const extglobClose=e=>{let t=e.close+(r.capture?")":"");if(e.type==="negate"){let n=w;if(e.inner&&e.inner.length>1&&e.inner.includes("/")){n=globstar(r)}if(n!==w||eos()||/^\)+$/.test(remaining())){t=e.close=`)$))${n}`}if(e.prev.type==="bos"){N.negatedExtglob=true}}push({type:"paren",extglob:true,value:B,output:t});decrement("parens")};if(r.fastpaths!==false&&!/(^[*!]|[/()[\]{}"])/.test(e)){let n=false;let s=e.replace(a,((e,t,r,u,s,o)=>{if(u==="\\"){n=true;return e}if(u==="?"){if(t){return t+u+(s?v.repeat(s.length):"")}if(o===0){return O+(s?v.repeat(s.length):"")}return v.repeat(r.length)}if(u==="."){return E.repeat(r.length)}if(u==="*"){if(t){return t+u+(s?w:"")}return w}return t?e:`\\${e}`}));if(n===true){if(r.unescape===true){s=s.replace(/\\/g,"")}else{s=s.replace(/\\+/g,(e=>e.length%2===0?"\\\\":e?"\\":""))}}if(s===e&&r.contains===true){N.output=e;return N}N.output=u.wrapOutput(s,N,t);return N}while(!eos()){B=D();if(B==="\0"){continue}if(B==="\\"){const e=G();if(e==="/"&&r.bash!==true){continue}if(e==="."||e===";"){continue}if(!e){B+="\\";push({type:"text",value:B});continue}const t=/^\\+/.exec(remaining());let n=0;if(t&&t[0].length>2){n=t[0].length;N.index+=n;if(n%2!==0){B+="\\"}}if(r.unescape===true){B=D()||""}else{B+=D()||""}if(N.brackets===0){push({type:"text",value:B});continue}}if(N.brackets>0&&(B!=="]"||P.value==="["||P.value==="[^")){if(r.posix!==false&&B===":"){const e=P.value.slice(1);if(e.includes("[")){P.posix=true;if(e.includes(":")){const e=P.value.lastIndexOf("[");const t=P.value.slice(0,e);const r=P.value.slice(e+2);const n=o[r];if(n){P.value=t+n;N.backtrack=true;D();if(!f.output&&A.indexOf(P)===1){f.output=d}continue}}}}if(B==="["&&G()!==":"||B==="-"&&G()==="]"){B=`\\${B}`}if(B==="]"&&(P.value==="["||P.value==="[^")){B=`\\${B}`}if(r.posix===true&&B==="!"&&P.value==="["){B="^"}P.value+=B;append({value:B});continue}if(N.quotes===1&&B!=='"'){B=u.escapeRegex(B);P.value+=B;append({value:B});continue}if(B==='"'){N.quotes=N.quotes===1?0:1;if(r.keepQuotes===true){push({type:"text",value:B})}continue}if(B==="("){increment("parens");push({type:"paren",value:B});continue}if(B===")"){if(N.parens===0&&r.strictBrackets===true){throw new SyntaxError(syntaxError("opening","("))}const e=k[k.length-1];if(e&&N.parens===e.parens+1){extglobClose(k.pop());continue}push({type:"paren",value:B,output:N.parens?")":"\\)"});decrement("parens");continue}if(B==="["){if(r.nobracket===true||!remaining().includes("]")){if(r.nobracket!==true&&r.strictBrackets===true){throw new SyntaxError(syntaxError("closing","]"))}B=`\\${B}`}else{increment("brackets")}push({type:"bracket",value:B});continue}if(B==="]"){if(r.nobracket===true||P&&P.type==="bracket"&&P.value.length===1){push({type:"text",value:B,output:`\\${B}`});continue}if(N.brackets===0){if(r.strictBrackets===true){throw new SyntaxError(syntaxError("opening","["))}push({type:"text",value:B,output:`\\${B}`});continue}decrement("brackets");const e=P.value.slice(1);if(P.posix!==true&&e[0]==="^"&&!e.includes("/")){B=`/${B}`}P.value+=B;append({value:B});if(r.literalBrackets===false||u.hasRegexChars(e)){continue}const t=u.escapeRegex(P.value);N.output=N.output.slice(0,-P.value.length);if(r.literalBrackets===true){N.output+=t;P.value=t;continue}P.value=`(${R}${t}|${P.value})`;N.output+=P.value;continue}if(B==="{"&&r.nobrace!==true){increment("braces");const e={type:"brace",value:B,output:"(",outputIndex:N.output.length,tokensIndex:N.tokens.length};I.push(e);push(e);continue}if(B==="}"){const e=I[I.length-1];if(r.nobrace===true||!e){push({type:"text",value:B,output:B});continue}let t=")";if(e.dots===true){const e=A.slice();const n=[];for(let t=e.length-1;t>=0;t--){A.pop();if(e[t].type==="brace"){break}if(e[t].type!=="dots"){n.unshift(e[t].value)}}t=expandRange(n,r);N.backtrack=true}if(e.comma!==true&&e.dots!==true){const r=N.output.slice(0,e.outputIndex);const n=N.tokens.slice(e.tokensIndex);e.value=e.output="\\{";B=t="\\}";N.output=r;for(const e of n){N.output+=e.output||e.value}}push({type:"brace",value:B,output:t});decrement("braces");I.pop();continue}if(B==="|"){if(k.length>0){k[k.length-1].conditions++}push({type:"text",value:B});continue}if(B===","){let e=B;const t=I[I.length-1];if(t&&M[M.length-1]==="braces"){t.comma=true;e="|"}push({type:"comma",value:B,output:e});continue}if(B==="/"){if(P.type==="dot"&&N.index===N.start+1){N.start=N.index+1;N.consumed="";N.output="";A.pop();P=f;continue}push({type:"slash",value:B,output:y});continue}if(B==="."){if(N.braces>0&&P.type==="dot"){if(P.value===".")P.output=E;const e=I[I.length-1];P.type="dots";P.output+=B;P.value+=B;e.dots=true;continue}if(N.braces+N.parens===0&&P.type!=="bos"&&P.type!=="slash"){push({type:"text",value:B,output:E});continue}push({type:"dot",value:B,output:E});continue}if(B==="?"){const e=P&&P.value==="(";if(!e&&r.noextglob!==true&&G()==="("&&G(2)!=="?"){extglobOpen("qmark",B);continue}if(P&&P.type==="paren"){const e=G();let t=B;if(e==="<"&&!u.supportsLookbehinds()){throw new Error("Node.js v10 or higher is required for regex lookbehinds")}if(P.value==="("&&!/[!=<:]/.test(e)||e==="<"&&!/<([!=]|\w+>)/.test(remaining())){t=`\\${B}`}push({type:"text",value:B,output:t});continue}if(r.dot!==true&&(P.type==="slash"||P.type==="bos")){push({type:"qmark",value:B,output:$});continue}push({type:"qmark",value:B,output:v});continue}if(B==="!"){if(r.noextglob!==true&&G()==="("){if(G(2)!=="?"||!/[!=<:]/.test(G(3))){extglobOpen("negate",B);continue}}if(r.nonegate!==true&&N.index===0){negate();continue}}if(B==="+"){if(r.noextglob!==true&&G()==="("&&G(2)!=="?"){extglobOpen("plus",B);continue}if(P&&P.value==="("||r.regex===false){push({type:"plus",value:B,output:C});continue}if(P&&(P.type==="bracket"||P.type==="paren"||P.type==="brace")||N.parens>0){push({type:"plus",value:B});continue}push({type:"plus",value:C});continue}if(B==="@"){if(r.noextglob!==true&&G()==="("&&G(2)!=="?"){push({type:"at",extglob:true,value:B,output:""});continue}push({type:"text",value:B});continue}if(B!=="*"){if(B==="$"||B==="^"){B=`\\${B}`}const e=i.exec(remaining());if(e){B+=e[0];N.index+=e[0].length}push({type:"text",value:B});continue}if(P&&(P.type==="globstar"||P.star===true)){P.type="star";P.star=true;P.value+=B;P.output=w;N.backtrack=true;N.globstar=true;consume(B);continue}let t=remaining();if(r.noextglob!==true&&/^\([^?]/.test(t)){extglobOpen("star",B);continue}if(P.type==="star"){if(r.noglobstar===true){consume(B);continue}const n=P.prev;const u=n.prev;const s=n.type==="slash"||n.type==="bos";const o=u&&(u.type==="star"||u.type==="globstar");if(r.bash===true&&(!s||t[0]&&t[0]!=="/")){push({type:"star",value:B,output:""});continue}const i=N.braces>0&&(n.type==="comma"||n.type==="brace");const a=k.length&&(n.type==="pipe"||n.type==="paren");if(!s&&n.type!=="paren"&&!i&&!a){push({type:"star",value:B,output:""});continue}while(t.slice(0,3)==="/**"){const r=e[N.index+4];if(r&&r!=="/"){break}t=t.slice(3);consume("/**",3)}if(n.type==="bos"&&eos()){P.type="globstar";P.value+=B;P.output=globstar(r);N.output=P.output;N.globstar=true;consume(B);continue}if(n.type==="slash"&&n.prev.type!=="bos"&&!o&&eos()){N.output=N.output.slice(0,-(n.output+P.output).length);n.output=`(?:${n.output}`;P.type="globstar";P.output=globstar(r)+(r.strictSlashes?")":"|$)");P.value+=B;N.globstar=true;N.output+=n.output+P.output;consume(B);continue}if(n.type==="slash"&&n.prev.type!=="bos"&&t[0]==="/"){const e=t[1]!==void 0?"|$":"";N.output=N.output.slice(0,-(n.output+P.output).length);n.output=`(?:${n.output}`;P.type="globstar";P.output=`${globstar(r)}${y}|${y}${e})`;P.value+=B;N.output+=n.output+P.output;N.globstar=true;consume(B+D());push({type:"slash",value:"/",output:""});continue}if(n.type==="bos"&&t[0]==="/"){P.type="globstar";P.value+=B;P.output=`(?:^|${y}|${globstar(r)}${y})`;N.output=P.output;N.globstar=true;consume(B+D());push({type:"slash",value:"/",output:""});continue}N.output=N.output.slice(0,-P.output.length);P.type="globstar";P.output=globstar(r);P.value+=B;N.output+=P.output;N.globstar=true;consume(B);continue}const n={type:"star",value:B,output:w};if(r.bash===true){n.output=".*?";if(P.type==="bos"||P.type==="slash"){n.output=L+n.output}push(n);continue}if(P&&(P.type==="bracket"||P.type==="paren")&&r.regex===true){n.output=B;push(n);continue}if(N.index===N.start||P.type==="slash"||P.type==="dot"){if(P.type==="dot"){N.output+=S;P.output+=S}else if(r.dot===true){N.output+=H;P.output+=H}else{N.output+=L;P.output+=L}if(G()!=="*"){N.output+=d;P.output+=d}}push(n)}while(N.brackets>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing","]"));N.output=u.escapeLast(N.output,"[");decrement("brackets")}while(N.parens>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing",")"));N.output=u.escapeLast(N.output,"(");decrement("parens")}while(N.braces>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing","}"));N.output=u.escapeLast(N.output,"{");decrement("braces")}if(r.strictSlashes!==true&&(P.type==="star"||P.type==="bracket")){push({type:"maybe_slash",value:"",output:`${y}?`})}if(N.backtrack===true){N.output="";for(const e of N.tokens){N.output+=e.output!=null?e.output:e.value;if(e.suffix){N.output+=e.suffix}}}return N};parse.fastpaths=(e,t)=>{const r={...t};const o=typeof r.maxLength==="number"?Math.min(s,r.maxLength):s;const i=e.length;if(i>o){throw new SyntaxError(`Input length: ${i}, exceeds maximum allowed length: ${o}`)}e=l[e]||e;const a=u.isWindows(t);const{DOT_LITERAL:c,SLASH_LITERAL:p,ONE_CHAR:f,DOTS_SLASH:A,NO_DOT:R,NO_DOTS:_,NO_DOTS_SLASH:h,STAR:g,START_ANCHOR:E}=n.globChars(a);const C=r.dot?_:R;const y=r.dot?h:R;const d=r.capture?"":"?:";const x={negated:false,prefix:""};let b=r.bash===true?".*?":g;if(r.capture){b=`(${b})`}const globstar=e=>{if(e.noglobstar===true)return b;return`(${d}(?:(?!${E}${e.dot?A:c}).)*?)`};const create=e=>{switch(e){case"*":return`${C}${f}${b}`;case".*":return`${c}${f}${b}`;case"*.*":return`${C}${b}${c}${f}${b}`;case"*/*":return`${C}${b}${p}${f}${y}${b}`;case"**":return C+globstar(r);case"**/*":return`(?:${C}${globstar(r)}${p})?${y}${f}${b}`;case"**/*.*":return`(?:${C}${globstar(r)}${p})?${y}${b}${c}${f}${b}`;case"**/.*":return`(?:${C}${globstar(r)}${p})?${c}${f}${b}`;default:{const t=/^(.*?)\.(\w+)$/.exec(e);if(!t)return;const r=create(t[1]);if(!r)return;return r+c+t[2]}}};const S=u.removePrefix(e,x);let H=create(S);if(H&&r.strictSlashes!==true){H+=`${p}?`}return H};e.exports=parse},683:(e,t,r)=>{const n=r(17);const u=r(700);const s=r(754);const o=r(513);const i=r(356);const isObject=e=>e&&typeof e==="object"&&!Array.isArray(e);const picomatch=(e,t,r=false)=>{if(Array.isArray(e)){const n=e.map((e=>picomatch(e,t,r)));const arrayMatcher=e=>{for(const t of n){const r=t(e);if(r)return r}return false};return arrayMatcher}const n=isObject(e)&&e.tokens&&e.input;if(e===""||typeof e!=="string"&&!n){throw new TypeError("Expected pattern to be a non-empty string")}const u=t||{};const s=o.isWindows(t);const i=n?picomatch.compileRe(e,t):picomatch.makeRe(e,t,false,true);const a=i.state;delete i.state;let isIgnored=()=>false;if(u.ignore){const e={...t,ignore:null,onMatch:null,onResult:null};isIgnored=picomatch(u.ignore,e,r)}const matcher=(r,n=false)=>{const{isMatch:o,match:l,output:c}=picomatch.test(r,i,t,{glob:e,posix:s});const p={glob:e,state:a,regex:i,posix:s,input:r,output:c,match:l,isMatch:o};if(typeof u.onResult==="function"){u.onResult(p)}if(o===false){p.isMatch=false;return n?p:false}if(isIgnored(r)){if(typeof u.onIgnore==="function"){u.onIgnore(p)}p.isMatch=false;return n?p:false}if(typeof u.onMatch==="function"){u.onMatch(p)}return n?p:true};if(r){matcher.state=a}return matcher};picomatch.test=(e,t,r,{glob:n,posix:u}={})=>{if(typeof e!=="string"){throw new TypeError("Expected input to be a string")}if(e===""){return{isMatch:false,output:""}}const s=r||{};const i=s.format||(u?o.toPosixSlashes:null);let a=e===n;let l=a&&i?i(e):e;if(a===false){l=i?i(e):e;a=l===n}if(a===false||s.capture===true){if(s.matchBase===true||s.basename===true){a=picomatch.matchBase(e,t,r,u)}else{a=t.exec(l)}}return{isMatch:Boolean(a),match:a,output:l}};picomatch.matchBase=(e,t,r,u=o.isWindows(r))=>{const s=t instanceof RegExp?t:picomatch.makeRe(t,r);return s.test(n.basename(e))};picomatch.isMatch=(e,t,r)=>picomatch(t,r)(e);picomatch.parse=(e,t)=>{if(Array.isArray(e))return e.map((e=>picomatch.parse(e,t)));return s(e,{...t,fastpaths:false})};picomatch.scan=(e,t)=>u(e,t);picomatch.compileRe=(e,t,r=false,n=false)=>{if(r===true){return e.output}const u=t||{};const s=u.contains?"":"^";const o=u.contains?"":"$";let i=`${s}(?:${e.output})${o}`;if(e&&e.negated===true){i=`^(?!${i}).*$`}const a=picomatch.toRegex(i,t);if(n===true){a.state=e}return a};picomatch.makeRe=(e,t,r=false,n=false)=>{if(!e||typeof e!=="string"){throw new TypeError("Expected a non-empty string")}const u=t||{};let o={negated:false,fastpaths:true};let i="";let a;if(e.startsWith("./")){e=e.slice(2);i=o.prefix="./"}if(u.fastpaths!==false&&(e[0]==="."||e[0]==="*")){a=s.fastpaths(e,t)}if(a===undefined){o=s(e,t);o.prefix=i+(o.prefix||"")}else{o.output=a}return picomatch.compileRe(o,t,r,n)};picomatch.toRegex=(e,t)=>{try{const r=t||{};return new RegExp(e,r.flags||(r.nocase?"i":""))}catch(e){if(t&&t.debug===true)throw e;return/$^/}};picomatch.constants=i;e.exports=picomatch},700:(e,t,r)=>{const n=r(513);const{CHAR_ASTERISK:u,CHAR_AT:s,CHAR_BACKWARD_SLASH:o,CHAR_COMMA:i,CHAR_DOT:a,CHAR_EXCLAMATION_MARK:l,CHAR_FORWARD_SLASH:c,CHAR_LEFT_CURLY_BRACE:p,CHAR_LEFT_PARENTHESES:f,CHAR_LEFT_SQUARE_BRACKET:A,CHAR_PLUS:R,CHAR_QUESTION_MARK:_,CHAR_RIGHT_CURLY_BRACE:h,CHAR_RIGHT_PARENTHESES:g,CHAR_RIGHT_SQUARE_BRACKET:E}=r(356);const isPathSeparator=e=>e===c||e===o;const depth=e=>{if(e.isPrefix!==true){e.depth=e.isGlobstar?Infinity:1}};const scan=(e,t)=>{const r=t||{};const C=e.length-1;const y=r.parts===true||r.scanToEnd===true;const d=[];const x=[];const b=[];let S=e;let H=-1;let v=0;let $=0;let m=false;let T=false;let L=false;let O=false;let w=false;let N=false;let k=false;let I=false;let M=false;let P=0;let B;let G;let D={value:"",depth:0,isGlob:false};const eos=()=>H>=C;const peek=()=>S.charCodeAt(H+1);const advance=()=>{B=G;return S.charCodeAt(++H)};while(H<C){G=advance();let e;if(G===o){k=D.backslashes=true;G=advance();if(G===p){N=true}continue}if(N===true||G===p){P++;while(eos()!==true&&(G=advance())){if(G===o){k=D.backslashes=true;advance();continue}if(G===p){P++;continue}if(N!==true&&G===a&&(G=advance())===a){m=D.isBrace=true;L=D.isGlob=true;M=true;if(y===true){continue}break}if(N!==true&&G===i){m=D.isBrace=true;L=D.isGlob=true;M=true;if(y===true){continue}break}if(G===h){P--;if(P===0){N=false;m=D.isBrace=true;M=true;break}}}if(y===true){continue}break}if(G===c){d.push(H);x.push(D);D={value:"",depth:0,isGlob:false};if(M===true)continue;if(B===a&&H===v+1){v+=2;continue}$=H+1;continue}if(r.noext!==true){const e=G===R||G===s||G===u||G===_||G===l;if(e===true&&peek()===f){L=D.isGlob=true;O=D.isExtglob=true;M=true;if(y===true){while(eos()!==true&&(G=advance())){if(G===o){k=D.backslashes=true;G=advance();continue}if(G===g){L=D.isGlob=true;M=true;break}}continue}break}}if(G===u){if(B===u)w=D.isGlobstar=true;L=D.isGlob=true;M=true;if(y===true){continue}break}if(G===_){L=D.isGlob=true;M=true;if(y===true){continue}break}if(G===A){while(eos()!==true&&(e=advance())){if(e===o){k=D.backslashes=true;advance();continue}if(e===E){T=D.isBracket=true;L=D.isGlob=true;M=true;break}}if(y===true){continue}break}if(r.nonegate!==true&&G===l&&H===v){I=D.negated=true;v++;continue}if(r.noparen!==true&&G===f){L=D.isGlob=true;if(y===true){while(eos()!==true&&(G=advance())){if(G===f){k=D.backslashes=true;G=advance();continue}if(G===g){M=true;break}}continue}break}if(L===true){M=true;if(y===true){continue}break}}if(r.noext===true){O=false;L=false}let U=S;let K="";let F="";if(v>0){K=S.slice(0,v);S=S.slice(v);$-=v}if(U&&L===true&&$>0){U=S.slice(0,$);F=S.slice($)}else if(L===true){U="";F=S}else{U=S}if(U&&U!==""&&U!=="/"&&U!==S){if(isPathSeparator(U.charCodeAt(U.length-1))){U=U.slice(0,-1)}}if(r.unescape===true){if(F)F=n.removeBackslashes(F);if(U&&k===true){U=n.removeBackslashes(U)}}const Q={prefix:K,input:e,start:v,base:U,glob:F,isBrace:m,isBracket:T,isGlob:L,isExtglob:O,isGlobstar:w,negated:I};if(r.tokens===true){Q.maxDepth=0;if(!isPathSeparator(G)){x.push(D)}Q.tokens=x}if(r.parts===true||r.tokens===true){let t;for(let n=0;n<d.length;n++){const u=t?t+1:v;const s=d[n];const o=e.slice(u,s);if(r.tokens){if(n===0&&v!==0){x[n].isPrefix=true;x[n].value=K}else{x[n].value=o}depth(x[n]);Q.maxDepth+=x[n].depth}if(n!==0||o!==""){b.push(o)}t=s}if(t&&t+1<e.length){const n=e.slice(t+1);b.push(n);if(r.tokens){x[x.length-1].value=n;depth(x[x.length-1]);Q.maxDepth+=x[x.length-1].depth}}Q.slashes=d;Q.parts=b}return Q};e.exports=scan},513:(e,t,r)=>{const n=r(17);const u=process.platform==="win32";const{REGEX_BACKSLASH:s,REGEX_REMOVE_BACKSLASH:o,REGEX_SPECIAL_CHARS:i,REGEX_SPECIAL_CHARS_GLOBAL:a}=r(356);t.isObject=e=>e!==null&&typeof e==="object"&&!Array.isArray(e);t.hasRegexChars=e=>i.test(e);t.isRegexChar=e=>e.length===1&&t.hasRegexChars(e);t.escapeRegex=e=>e.replace(a,"\\$1");t.toPosixSlashes=e=>e.replace(s,"/");t.removeBackslashes=e=>e.replace(o,(e=>e==="\\"?"":e));t.supportsLookbehinds=()=>{const e=process.version.slice(1).split(".").map(Number);if(e.length===3&&e[0]>=9||e[0]===8&&e[1]>=10){return true}return false};t.isWindows=e=>{if(e&&typeof e.windows==="boolean"){return e.windows}return u===true||n.sep==="\\"};t.escapeLast=(e,r,n)=>{const u=e.lastIndexOf(r,n);if(u===-1)return e;if(e[u-1]==="\\")return t.escapeLast(e,r,u-1);return`${e.slice(0,u)}\\${e.slice(u)}`};t.removePrefix=(e,t={})=>{let r=e;if(r.startsWith("./")){r=r.slice(2);t.prefix="./"}return r};t.wrapOutput=(e,t={},r={})=>{const n=r.contains?"":"^";const u=r.contains?"":"$";let s=`${n}(?:${e})${u}`;if(t.negated===true){s=`(?:^(?!${s}).*$)`}return s}},492:(e,t,r)=>{
/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */
const n=r(357);const toRegexRange=(e,t,r)=>{if(n(e)===false){throw new TypeError("toRegexRange: expected the first argument to be a number")}if(t===void 0||e===t){return String(e)}if(n(t)===false){throw new TypeError("toRegexRange: expected the second argument to be a number.")}let u={relaxZeros:true,...r};if(typeof u.strictZeros==="boolean"){u.relaxZeros=u.strictZeros===false}let s=String(u.relaxZeros);let o=String(u.shorthand);let i=String(u.capture);let a=String(u.wrap);let l=e+":"+t+"="+s+o+i+a;if(toRegexRange.cache.hasOwnProperty(l)){return toRegexRange.cache[l].result}let c=Math.min(e,t);let p=Math.max(e,t);if(Math.abs(c-p)===1){let r=e+"|"+t;if(u.capture){return`(${r})`}if(u.wrap===false){return r}return`(?:${r})`}let f=hasPadding(e)||hasPadding(t);let A={min:e,max:t,a:c,b:p};let R=[];let _=[];if(f){A.isPadded=f;A.maxLen=String(A.max).length}if(c<0){let e=p<0?Math.abs(p):1;_=splitToPatterns(e,Math.abs(c),A,u);c=A.a=0}if(p>=0){R=splitToPatterns(c,p,A,u)}A.negatives=_;A.positives=R;A.result=collatePatterns(_,R,u);if(u.capture===true){A.result=`(${A.result})`}else if(u.wrap!==false&&R.length+_.length>1){A.result=`(?:${A.result})`}toRegexRange.cache[l]=A;return A.result};function collatePatterns(e,t,r){let n=filterPatterns(e,t,"-",false,r)||[];let u=filterPatterns(t,e,"",false,r)||[];let s=filterPatterns(e,t,"-?",true,r)||[];let o=n.concat(s).concat(u);return o.join("|")}function splitToRanges(e,t){let r=1;let n=1;let u=countNines(e,r);let s=new Set([t]);while(e<=u&&u<=t){s.add(u);r+=1;u=countNines(e,r)}u=countZeros(t+1,n)-1;while(e<u&&u<=t){s.add(u);n+=1;u=countZeros(t+1,n)-1}s=[...s];s.sort(compare);return s}function rangeToPattern(e,t,r){if(e===t){return{pattern:e,count:[],digits:0}}let n=zip(e,t);let u=n.length;let s="";let o=0;for(let e=0;e<u;e++){let[t,u]=n[e];if(t===u){s+=t}else if(t!=="0"||u!=="9"){s+=toCharacterClass(t,u,r)}else{o++}}if(o){s+=r.shorthand===true?"\\d":"[0-9]"}return{pattern:s,count:[o],digits:u}}function splitToPatterns(e,t,r,n){let u=splitToRanges(e,t);let s=[];let o=e;let i;for(let e=0;e<u.length;e++){let t=u[e];let a=rangeToPattern(String(o),String(t),n);let l="";if(!r.isPadded&&i&&i.pattern===a.pattern){if(i.count.length>1){i.count.pop()}i.count.push(a.count[0]);i.string=i.pattern+toQuantifier(i.count);o=t+1;continue}if(r.isPadded){l=padZeros(t,r,n)}a.string=l+a.pattern+toQuantifier(a.count);s.push(a);o=t+1;i=a}return s}function filterPatterns(e,t,r,n,u){let s=[];for(let u of e){let{string:e}=u;if(!n&&!contains(t,"string",e)){s.push(r+e)}if(n&&contains(t,"string",e)){s.push(r+e)}}return s}function zip(e,t){let r=[];for(let n=0;n<e.length;n++)r.push([e[n],t[n]]);return r}function compare(e,t){return e>t?1:t>e?-1:0}function contains(e,t,r){return e.some((e=>e[t]===r))}function countNines(e,t){return Number(String(e).slice(0,-t)+"9".repeat(t))}function countZeros(e,t){return e-e%Math.pow(10,t)}function toQuantifier(e){let[t=0,r=""]=e;if(r||t>1){return`{${t+(r?","+r:"")}}`}return""}function toCharacterClass(e,t,r){return`[${e}${t-e===1?"":"-"}${t}]`}function hasPadding(e){return/^-?(0+)\d/.test(e)}function padZeros(e,t,r){if(!t.isPadded){return e}let n=Math.abs(t.maxLen-String(e).length);let u=r.relaxZeros!==false;switch(n){case 0:return"";case 1:return u?"0?":"0";case 2:return u?"0{0,2}":"00";default:{return u?`0{0,${n}}`:`0{${n}}`}}}toRegexRange.cache={};toRegexRange.clearCache=()=>toRegexRange.cache={};e.exports=toRegexRange},17:e=>{e.exports=require("path")},837:e=>{e.exports=require("util")}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var u=t[r]={exports:{}};var s=true;try{e[r](u,u.exports,__nccwpck_require__);s=false}finally{if(s)delete t[r]}return u.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(971);module.exports=r})();