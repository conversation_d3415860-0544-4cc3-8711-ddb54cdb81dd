module.exports = "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __esm = (fn, res) => function __init() {\n  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;\n};\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// <define:process>\nvar init_define_process = __esm({\n  \"<define:process>\"() {\n  }\n});\n\n// ../format/dist/index.js\nvar require_dist = __commonJS({\n  \"../format/dist/index.js\"(exports, module2) {\n    \"use strict\";\n    init_define_process();\n    var __defProp2 = Object.defineProperty;\n    var __getOwnPropDesc2 = Object.getOwnPropertyDescriptor;\n    var __getOwnPropNames2 = Object.getOwnPropertyNames;\n    var __hasOwnProp2 = Object.prototype.hasOwnProperty;\n    var __export2 = /* @__PURE__ */ __name((target, all) => {\n      for (var name in all)\n        __defProp2(target, name, { get: all[name], enumerable: true });\n    }, \"__export\");\n    var __copyProps2 = /* @__PURE__ */ __name((to, from, except, desc) => {\n      if (from && typeof from === \"object\" || typeof from === \"function\") {\n        for (let key of __getOwnPropNames2(from))\n          if (!__hasOwnProp2.call(to, key) && key !== except)\n            __defProp2(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc2(from, key)) || desc.enumerable });\n      }\n      return to;\n    }, \"__copyProps\");\n    var __toCommonJS2 = /* @__PURE__ */ __name((mod) => __copyProps2(__defProp2({}, \"__esModule\", { value: true }), mod), \"__toCommonJS\");\n    var src_exports = {};\n    __export2(src_exports, {\n      createFormat: () => createFormat2\n    });\n    module2.exports = __toCommonJS2(src_exports);\n    var ReflectGetOwnPropertyDescriptor = Reflect.getOwnPropertyDescriptor;\n    function GetOwnGetter(target, key) {\n      const descriptor = ReflectGetOwnPropertyDescriptor(target, key);\n      return descriptor ? descriptor.get : void 0;\n    }\n    __name(GetOwnGetter, \"GetOwnGetter\");\n    var ReflectGetPrototypeOf = Reflect.getPrototypeOf;\n    var TypedArray = ReflectGetPrototypeOf(Uint8Array);\n    var ArrayPrototypeFilter = Array.prototype.filter;\n    var ArrayPrototypePush = Array.prototype.push;\n    var DatePrototypeGetTime = Date.prototype.getTime;\n    var DatePrototypeToISOString = Date.prototype.toISOString;\n    var ObjectGetOwnPropertyDescriptors = Object.getOwnPropertyDescriptors;\n    var ObjectGetOwnPropertyNames = Object.getOwnPropertyNames;\n    var ObjectGetOwnPropertySymbols = Object.getOwnPropertySymbols;\n    var ObjectKeys = Object.keys;\n    var ObjectPrototypePropertyIsEnumerable = Object.prototype.propertyIsEnumerable;\n    var ObjectPrototypeToString = Object.prototype.toString;\n    var MapPrototypeGetSize = GetOwnGetter(Map.prototype, \"size\");\n    var SetPrototypeGetSize = GetOwnGetter(Set.prototype, \"size\");\n    var StringPrototypeIncludes = String.prototype.includes;\n    var SymbolIterator = Symbol.iterator;\n    var SymbolPrototypeToString = Symbol.prototype.toString;\n    var TypedArrayPrototypeGetLength = GetOwnGetter(\n      TypedArray.prototype,\n      \"length\"\n    );\n    var typedArrayStrings = /* @__PURE__ */ new Set([\n      \"[object BigInt64Array]\",\n      \"[object BigUint64Array]\",\n      \"[object Float32Array]\",\n      \"[object Float64Array]\",\n      \"[object Int8Array]\",\n      \"[object Int16Array]\",\n      \"[object Int32Array]\",\n      \"[object Uint8Array]\",\n      \"[object Uint8ClampedArray]\",\n      \"[object Uint16Array]\",\n      \"[object Uint32Array]\"\n    ]);\n    function getOwnNonIndexProperties(object, filter) {\n      const indexes = Array.isArray(object) || isTypedArray(object) ? new Set([...object.keys()].map((v) => v.toString())) : void 0;\n      return Object.entries(ObjectGetOwnPropertyDescriptors(object)).filter(([key, desc]) => {\n        if (indexes && indexes.has(key)) {\n          return false;\n        }\n        if (filter === 1 && !desc.enumerable) {\n          return false;\n        }\n        return true;\n      }).map(([key]) => key);\n    }\n    __name(getOwnNonIndexProperties, \"getOwnNonIndexProperties\");\n    var isTypedArray = /* @__PURE__ */ __name((value) => kind(value, \"object\") && typedArrayStrings.has(ObjectPrototypeToString.call(value)), \"isTypedArray\");\n    function kind(value, type) {\n      return typeof value === type;\n    }\n    __name(kind, \"kind\");\n    var getConstructorName = /* @__PURE__ */ __name((object) => {\n      var _a;\n      return (_a = object.constructor) == null ? void 0 : _a.name;\n    }, \"getConstructorName\");\n    var getPrefix = /* @__PURE__ */ __name((constructor = \"\", size = \"\") => `${constructor}${size} `, \"getPrefix\");\n    function createFormat2(opts = {}) {\n      if (opts.customInspectSymbol === void 0) {\n        opts.customInspectSymbol = Symbol.for(\"edge-runtime.inspect.custom\");\n      }\n      if (opts.formatError === void 0) {\n        opts.formatError = (error2) => `[${Error.prototype.toString.call(error2)}]`;\n      }\n      const { formatError, customInspectSymbol } = opts;\n      function format2(...args) {\n        const [firstArg] = args;\n        if (!kind(firstArg, \"string\")) {\n          if (hasCustomSymbol(firstArg, customInspectSymbol)) {\n            return format2(firstArg[customInspectSymbol]({ format: format2 }));\n          } else {\n            return args.map((item) => inspect(item, { customInspectSymbol })).join(\" \");\n          }\n        }\n        let index = 1;\n        let str = String(firstArg).replace(/%[sjdOoif%]/g, (token) => {\n          if (token === \"%%\")\n            return \"%\";\n          if (index >= args.length)\n            return token;\n          switch (token) {\n            case \"%s\": {\n              const arg = args[index++];\n              if (hasCustomSymbol(arg, customInspectSymbol)) {\n                return format2(arg[customInspectSymbol]({ format: format2 }));\n              } else if (isDate(arg) || isError(arg) || kind(arg, \"bigint\")) {\n                return format2(arg);\n              } else {\n                return String(arg);\n              }\n            }\n            case \"%j\":\n              return safeStringify(args[index++]);\n            case \"%d\": {\n              const arg = args[index++];\n              if (kind(arg, \"bigint\")) {\n                return format2(arg);\n              } else {\n                return String(Number(arg));\n              }\n            }\n            case \"%O\":\n              return inspect(args[index++], { customInspectSymbol });\n            case \"%o\":\n              return inspect(args[index++], {\n                customInspectSymbol,\n                showHidden: true,\n                depth: 4\n              });\n            case \"%i\": {\n              const arg = args[index++];\n              if (kind(arg, \"bigint\")) {\n                return format2(arg);\n              } else {\n                return String(parseInt(arg, 10));\n              }\n            }\n            case \"%f\":\n              return String(parseFloat(args[index++]));\n            default:\n              return token;\n          }\n        });\n        for (let arg = args[index]; index < args.length; arg = args[++index]) {\n          if (arg === null || !kind(arg, \"object\")) {\n            str += \" \" + arg;\n          } else {\n            str += \" \" + inspect(arg);\n          }\n        }\n        return str;\n      }\n      __name(format2, \"format\");\n      function formatValue(ctx, value, recurseTimes) {\n        if (hasCustomSymbol(value, customInspectSymbol)) {\n          return format2(value[customInspectSymbol]({ format: format2 }));\n        }\n        const formattedPrimitive = formatPrimitive(value);\n        if (formattedPrimitive !== void 0) {\n          return formattedPrimitive;\n        }\n        if (ctx.seen.includes(value)) {\n          let index = 1;\n          if (ctx.circular === void 0) {\n            ctx.circular = /* @__PURE__ */ new Map();\n            ctx.circular.set(value, index);\n          } else {\n            index = ctx.circular.get(value);\n            if (index === void 0) {\n              index = ctx.circular.size + 1;\n              ctx.circular.set(value, index);\n            }\n          }\n          return `[Circular *${index}]`;\n        }\n        return formatRaw(ctx, value, recurseTimes);\n      }\n      __name(formatValue, \"formatValue\");\n      function formatRaw(ctx, value, recurseTimes) {\n        let keys = [];\n        const constructor = getConstructorName(value);\n        let base = \"\";\n        let formatter = /* @__PURE__ */ __name(() => [], \"formatter\");\n        let braces = [\"\", \"\"];\n        let noIterator = true;\n        const filter = ctx.showHidden ? 0 : 1;\n        if (SymbolIterator in value) {\n          noIterator = false;\n          if (Array.isArray(value)) {\n            const prefix = constructor !== \"Array\" ? getPrefix(constructor, `(${value.length})`) : \"\";\n            keys = getOwnNonIndexProperties(value, filter);\n            braces = [`${prefix}[`, \"]\"];\n            if (value.length === 0 && keys.length === 0) {\n              return `${braces[0]}]`;\n            }\n            formatter = formatArray;\n          } else if (isSet(value)) {\n            const size = SetPrototypeGetSize.call(value);\n            const prefix = getPrefix(constructor, `(${size})`);\n            keys = getKeys(value, ctx.showHidden);\n            formatter = formatSet;\n            if (size === 0 && keys.length === 0) {\n              return `${prefix}{}`;\n            }\n            braces = [`${prefix}{`, \"}\"];\n          } else if (isMap(value)) {\n            const size = MapPrototypeGetSize.call(value);\n            const prefix = getPrefix(constructor, `(${size})`);\n            keys = getKeys(value, ctx.showHidden);\n            formatter = formatMap;\n            if (size === 0 && keys.length === 0) {\n              return `${prefix}{}`;\n            }\n            braces = [`${prefix}{`, \"}\"];\n          } else if (isTypedArray(value)) {\n            keys = getOwnNonIndexProperties(value, filter);\n            const size = TypedArrayPrototypeGetLength.call(value);\n            const prefix = getPrefix(constructor, `(${size})`);\n            braces = [`${prefix}[`, \"]\"];\n            if (value.length === 0 && keys.length === 0)\n              return `${braces[0]}]`;\n            formatter = formatTypedArray.bind(null, size);\n          } else {\n            noIterator = true;\n          }\n        }\n        if (noIterator) {\n          keys = getKeys(value, ctx.showHidden);\n          braces = [\"{\", \"}\"];\n          if (constructor === void 0) {\n            if (keys.length === 0) {\n              return `[Object: null prototype] {}`;\n            }\n          } else if (constructor === \"Object\") {\n            if (keys.length === 0) {\n              return `{}`;\n            }\n          } else if (kind(value, \"function\")) {\n            base = `[Function${value.name ? \": \" + value.name : \"\"}]`;\n            if (keys.length === 0) {\n              return base;\n            }\n          } else if (isRegExp(value)) {\n            base = RegExp.prototype.toString.call(value);\n            if (keys.length === 0) {\n              return base;\n            }\n            base = \" \" + base;\n          } else if (isDate(value)) {\n            base = Number.isNaN(DatePrototypeGetTime.call(value)) ? Date.prototype.toString.call(value) : DatePrototypeToISOString.call(value);\n            if (keys.length === 0) {\n              return base;\n            }\n            base = \" \" + base;\n          } else if (isError(value)) {\n            base = formatError(value);\n            if (keys.length === 0) {\n              return base;\n            }\n            base = \" \" + base;\n          } else if (hasCustomSymbol(value, ctx.customInspectSymbol)) {\n            base = format2(value[ctx.customInspectSymbol]({ format: format2 }));\n            if (keys.length === 0) {\n              return base;\n            }\n            base = \" \" + base;\n          } else {\n            braces[0] = `${getPrefix(constructor)}{`;\n          }\n        }\n        if (recurseTimes && recurseTimes < 0) {\n          return isRegExp(value) ? RegExp.prototype.toString.call(value) : \"[Object]\";\n        }\n        ctx.seen.push(value);\n        const visibleKeys = new Set(keys);\n        const output = formatter(ctx, value, recurseTimes, visibleKeys, keys);\n        for (let i = 0; i < keys.length; i++) {\n          output.push(\n            formatProperty(\n              ctx,\n              value,\n              recurseTimes,\n              visibleKeys,\n              keys[i],\n              false\n            )\n          );\n        }\n        if (ctx.circular !== void 0) {\n          const index = ctx.circular.get(value);\n          if (index !== void 0) {\n            const reference = `<ref *${index}>`;\n            base = base === \"\" ? reference : `${reference} ${base}`;\n          }\n        }\n        ctx.seen.pop();\n        return reduceToSingleString(output, base, braces);\n      }\n      __name(formatRaw, \"formatRaw\");\n      function inspect(value, opts2) {\n        opts2 = Object.assign({ seen: [], depth: 2 }, opts2);\n        return formatValue(opts2, value, opts2.depth);\n      }\n      __name(inspect, \"inspect\");\n      function formatProperty(ctx, value, recurseTimes, visibleKeys, key, isArray) {\n        let name;\n        let str;\n        const desc = Object.getOwnPropertyDescriptor(value, key) || {\n          value: value[key]\n        };\n        if (desc.value !== void 0) {\n          str = formatValue(ctx, desc.value, recurseTimes);\n        } else if (desc.get) {\n          str = desc.set ? \"[Getter/Setter]\" : \"[Getter]\";\n        } else if (desc.set) {\n          str = \"[Setter]\";\n        } else {\n          str = \"undefined\";\n        }\n        if (isArray) {\n          return str;\n        }\n        if (kind(key, \"symbol\")) {\n          name = `[${SymbolPrototypeToString.call(key)}]`;\n        } else if (!visibleKeys.has(key)) {\n          name = \"[\" + key + \"]\";\n        } else {\n          name = key;\n        }\n        return `${name}: ${str}`;\n      }\n      __name(formatProperty, \"formatProperty\");\n      function formatArray(ctx, value, recurseTimes, visibleKeys) {\n        const output = [];\n        for (let index = 0; index < value.length; ++index) {\n          if (Object.prototype.hasOwnProperty.call(value, String(index))) {\n            output.push(\n              formatProperty(\n                ctx,\n                value,\n                recurseTimes,\n                visibleKeys,\n                String(index),\n                true\n              )\n            );\n          } else {\n            output.push(\"\");\n          }\n        }\n        return output;\n      }\n      __name(formatArray, \"formatArray\");\n      function formatTypedArray(length, ctx, value, recurseTimes) {\n        const output = new Array(length);\n        for (let i = 0; i < length; ++i) {\n          output[i] = value.length > 0 && kind(value[0], \"number\") ? String(value[i]) : formatBigInt(value[i]);\n        }\n        if (ctx.showHidden) {\n          for (const key of [\n            \"BYTES_PER_ELEMENT\",\n            \"length\",\n            \"byteLength\",\n            \"byteOffset\",\n            \"buffer\"\n          ]) {\n            const str = formatValue(ctx, value[key], recurseTimes);\n            ArrayPrototypePush.call(output, `[${String(key)}]: ${str}`);\n          }\n        }\n        return output;\n      }\n      __name(formatTypedArray, \"formatTypedArray\");\n      function formatSet(ctx, value, recurseTimes) {\n        const output = [];\n        for (const v of value) {\n          ArrayPrototypePush.call(output, formatValue(ctx, v, recurseTimes));\n        }\n        return output;\n      }\n      __name(formatSet, \"formatSet\");\n      function formatMap(ctx, value, recurseTimes) {\n        const output = [];\n        for (const { 0: k, 1: v } of value) {\n          output.push(\n            `${formatValue(ctx, k, recurseTimes)} => ${formatValue(\n              ctx,\n              v,\n              recurseTimes\n            )}`\n          );\n        }\n        return output;\n      }\n      __name(formatMap, \"formatMap\");\n      return format2;\n    }\n    __name(createFormat2, \"createFormat\");\n    var formatBigInt = /* @__PURE__ */ __name((bigint) => `${bigint}n`, \"formatBigInt\");\n    function formatPrimitive(value) {\n      if (value === null)\n        return \"null\";\n      if (value === void 0)\n        return \"undefined\";\n      if (kind(value, \"string\")) {\n        return `'${JSON.stringify(value).replace(/^\"|\"$/g, \"\").replace(/'/g, \"\\\\'\").replace(/\\\\\"/g, '\"')}'`;\n      }\n      if (kind(value, \"boolean\"))\n        return \"\" + value;\n      if (kind(value, \"number\"))\n        return \"\" + value;\n      if (kind(value, \"bigint\"))\n        return formatBigInt(value);\n      if (kind(value, \"symbol\"))\n        return value.toString();\n    }\n    __name(formatPrimitive, \"formatPrimitive\");\n    function hasCustomSymbol(value, customInspectSymbol) {\n      return value !== null && kind(value, \"object\") && customInspectSymbol in value && kind(value[customInspectSymbol], \"function\");\n    }\n    __name(hasCustomSymbol, \"hasCustomSymbol\");\n    function isRegExp(value) {\n      return kind(value, \"object\") && Object.prototype.toString.call(value) === \"[object RegExp]\";\n    }\n    __name(isRegExp, \"isRegExp\");\n    function isDate(value) {\n      return kind(value, \"object\") && Object.prototype.toString.call(value) === \"[object Date]\";\n    }\n    __name(isDate, \"isDate\");\n    function isError(value) {\n      return kind(value, \"object\") && (Object.prototype.toString.call(value) === \"[object Error]\" || value instanceof Error);\n    }\n    __name(isError, \"isError\");\n    function isMap(value) {\n      return kind(value, \"object\") && Object.prototype.toString.call(value) === \"[object Map]\";\n    }\n    __name(isMap, \"isMap\");\n    function isSet(value) {\n      return kind(value, \"object\") && Object.prototype.toString.call(value) === \"[object Set]\";\n    }\n    __name(isSet, \"isSet\");\n    function isBelowBreakLength(output, start, base) {\n      const breakLength = 80;\n      let totalLength = output.length + start;\n      if (totalLength + output.length > breakLength) {\n        return false;\n      }\n      for (let i = 0; i < output.length; i++) {\n        totalLength += output[i].length;\n        if (totalLength > breakLength) {\n          return false;\n        }\n      }\n      return base === \"\" || !StringPrototypeIncludes.call(base, \"\\n\");\n    }\n    __name(isBelowBreakLength, \"isBelowBreakLength\");\n    function reduceToSingleString(output, base, braces) {\n      const start = output.length + braces[0].length + base.length + 10;\n      if (!isBelowBreakLength(output, start, base)) {\n        return (base ? base + \" \" : \"\") + braces[0] + \"\\n  \" + output.join(\",\\n  \") + \"\\n\" + braces[1];\n      }\n      return ((base ? base + \" \" : \"\") + braces[0] + \" \" + output.join(\", \") + \" \" + braces[1]).trim();\n    }\n    __name(reduceToSingleString, \"reduceToSingleString\");\n    function safeStringify(input) {\n      if (Array.isArray(input)) {\n        input = input.map(\n          (element) => JSON.parse(JSON.stringify(element, makeCircularReplacer()))\n        );\n      }\n      return JSON.stringify(input, makeCircularReplacer());\n    }\n    __name(safeStringify, \"safeStringify\");\n    function makeCircularReplacer() {\n      const seen = /* @__PURE__ */ new WeakSet();\n      return (key, value) => {\n        if (value !== null && kind(value, \"object\")) {\n          if (seen.has(value))\n            return \"[Circular]\";\n          seen.add(value);\n        }\n        return value;\n      };\n    }\n    __name(makeCircularReplacer, \"makeCircularReplacer\");\n    function getKeys(value, showHidden = false) {\n      let keys;\n      const symbols = ObjectGetOwnPropertySymbols(value);\n      if (showHidden) {\n        keys = ObjectGetOwnPropertyNames(value);\n        if (symbols.length !== 0)\n          ArrayPrototypePush.apply(keys, symbols);\n      } else {\n        try {\n          keys = ObjectKeys(value);\n        } catch (err) {\n          keys = ObjectGetOwnPropertyNames(value);\n        }\n        if (symbols.length !== 0) {\n          const filter = /* @__PURE__ */ __name((key) => ObjectPrototypePropertyIsEnumerable.call(value, key), \"filter\");\n          ArrayPrototypePush.apply(keys, ArrayPrototypeFilter.call(symbols, filter));\n        }\n      }\n      return keys;\n    }\n    __name(getKeys, \"getKeys\");\n  }\n});\n\n// src/primitives/console.js\nvar console_exports = {};\n__export(console_exports, {\n  console: () => konsole\n});\nmodule.exports = __toCommonJS(console_exports);\ninit_define_process();\nvar import_format = __toESM(require_dist());\nvar format = (0, import_format.createFormat)();\nvar bareError = console.error.bind(console);\nvar bareLog = console.log.bind(console);\nvar assert = console.assert.bind(console);\nvar time = console.time.bind(console);\nvar timeEnd = console.timeEnd.bind(console);\nvar timeLog = console.timeLog.bind(console);\nvar trace = console.trace.bind(console);\nvar error = /* @__PURE__ */ __name((...args) => bareError(format(...args)), \"error\");\nvar log = /* @__PURE__ */ __name((...args) => bareLog(format(...args)), \"log\");\nvar konsole = {\n  assert: (assertion, ...args) => assert(assertion, format(...args)),\n  count: console.count.bind(console),\n  debug: log,\n  dir: console.dir.bind(console),\n  error,\n  info: log,\n  log,\n  time: (...args) => time(format(...args)),\n  timeEnd: (...args) => timeEnd(format(...args)),\n  timeLog,\n  trace,\n  warn: error\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  console\n});\n"