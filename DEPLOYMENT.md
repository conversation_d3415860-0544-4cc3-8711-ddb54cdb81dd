# 🚀 Deployment Guide - Best Lyfe Fashion

## 📋 Deployment to BanaHosting

### 🔧 Prerequisites
- BanaHosting Professional Deluxe Unlimited SSD
- Node.js enabled on hosting
- Domain: bestlyfefashion.com

### 📦 Step 1: Prepare the Build
```bash
# Install dependencies
npm install

# Build for production
npm run build
```

### 📁 Step 2: Upload Files
Upload these files/folders to your hosting:
- `.next/` (build output)
- `public/`
- `package.json`
- `package-lock.json`
- `.env.production` (rename to .env.local on server)
- `next.config.js`
- All other project files

### ⚙️ Step 3: Server Setup
1. Access your BanaHosting cPanel
2. Go to Node.js Selector
3. Create new Node.js app:
   - **Node.js version**: Latest LTS
   - **Application root**: public_html
   - **Application URL**: bestlyfefashion.com
   - **Application startup file**: server.js

### 🗄️ Step 4: Environment Variables
In cPanel Node.js app settings, add:
```
NODE_ENV=production
MONGODB_URI=mongodb+srv://anthonchess:<EMAIL>/bestlyfefashion
NEXTAUTH_URL=https://bestlyfefashion.com
```

### 🚀 Step 5: Install Dependencies & Start
```bash
# In cPanel Terminal or SSH
npm install --production
npm start
```

### 🌐 Step 6: Configure Domain
- Point bestlyfefashion.com to your hosting IP
- Enable SSL certificate in cPanel
- Test the site at https://bestlyfefashion.com

### 🔍 Troubleshooting Error 503

#### Common Causes and Solutions:

1. **🚫 Application Not Started**
   ```bash
   # In cPanel Terminal, check if app is running:
   pm2 list

   # If not running, start it:
   pm2 start ecosystem.config.js --env production
   ```

2. **🔧 Wrong Startup File**
   - Ensure "Application startup file" in cPanel is set to: `server.js`
   - NOT `app.js` or `index.js`

3. **🌐 Port Configuration Issues**
   - BanaHosting may require specific port configuration
   - Try setting PORT environment variable to different values: 3000, 8080, or 80

4. **📁 Missing Build Files**
   ```bash
   # Make sure you built the project:
   npm run build

   # Check if .next folder exists and has content
   ls -la .next/
   ```

5. **🗄️ Database Connection Issues**
   ```bash
   # Test MongoDB connection:
   node health-check.js
   ```

6. **📋 Environment Variables**
   - Verify in cPanel Node.js app settings:
     - `NODE_ENV=production`
     - `MONGODB_URI=your_connection_string`
     - `PORT=3000`

7. **🔒 File Permissions**
   ```bash
   # Set correct permissions:
   chmod 755 server.js
   chmod -R 755 .next/
   chmod -R 755 public/
   ```

8. **📊 Check Logs**
   ```bash
   # View application logs:
   pm2 logs bestlyfefashion

   # Or check log files:
   tail -f logs/err.log
   tail -f logs/out.log
   ```

#### Step-by-Step Debugging:

1. **Run Health Check**:
   ```bash
   node health-check.js
   ```

2. **Restart Application**:
   ```bash
   pm2 restart bestlyfefashion
   ```

3. **Check Application Status**:
   ```bash
   pm2 status
   ```

4. **Monitor Logs in Real-time**:
   ```bash
   pm2 logs bestlyfefashion --lines 50
   ```

### 📞 Support
- BanaHosting Support: https://banahosting.com/support
- Documentation: Check cPanel Node.js documentation
- Live Chat: Available in cPanel
