const mongoose = require('mongoose')
require('dotenv').config({ path: '.env.local' })

const MONGODB_URI = process.env.MONGODB_URI

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI not found in environment variables')
  process.exit(1)
}

console.log('🔗 Testing MongoDB connection...')
console.log('📍 URI:', MONGODB_URI.replace(/:[^:@]*@/, ':****@'))
console.log('💡 If connection fails, check:')
console.log('   1. IP Whitelist in MongoDB Atlas Network Access')
console.log('   2. Database user credentials')
console.log('   3. Cluster status')

const connectOptions = {
  bufferCommands: false,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4, // Use IPv4, skip trying IPv6
  retryWrites: true,
  w: 'majority'
}

async function testConnection() {
  try {
    console.log('⏳ Attempting to connect...')
    
    await mongoose.connect(MONGODB_URI, connectOptions)
    
    console.log('✅ Successfully connected to MongoDB!')
    console.log('📊 Connection state:', mongoose.connection.readyState)
    console.log('🏷️  Database name:', mongoose.connection.name)
    console.log('🌐 Host:', mongoose.connection.host)
    
    // Test a simple operation
    const collections = await mongoose.connection.db.listCollections().toArray()
    console.log('📁 Available collections:', collections.map(c => c.name))
    
    await mongoose.disconnect()
    console.log('🔌 Disconnected successfully')
    
  } catch (error) {
    console.error('❌ Connection failed:')
    console.error('Error name:', error.name)
    console.error('Error message:', error.message)
    
    if (error.code) {
      console.error('Error code:', error.code)
    }
    
    if (error.codeName) {
      console.error('Error codeName:', error.codeName)
    }
    
    process.exit(1)
  }
}

testConnection()
