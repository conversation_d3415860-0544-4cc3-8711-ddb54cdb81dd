module.exports = "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/primitives/encoding.js\nvar encoding_exports = {};\n__export(encoding_exports, {\n  atob: () => atob,\n  btoa: () => btoa\n});\nmodule.exports = __toCommonJS(encoding_exports);\nvar atob = /* @__PURE__ */ __name((enc) => Buffer.from(enc, \"base64\").toString(\"binary\"), \"atob\");\nvar btoa = /* @__PURE__ */ __name((str) => Buffer.from(str, \"binary\").toString(\"base64\"), \"btoa\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  atob,\n  btoa\n});\n"