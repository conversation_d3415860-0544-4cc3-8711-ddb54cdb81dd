# 📋 BanaHosting Upload Instructions

## Files to Upload:
Upload ALL contents of this deployment-package folder to your public_html directory.

## Required Files:
✅ package.json
✅ package-lock.json  
✅ server.js
✅ next.config.js
✅ ecosystem.config.js
✅ .next/ (entire folder)
✅ public/ (entire folder)
✅ health-check.js

## Environment Variables:
1. Rename .env.local.template to .env.local
2. Or set these variables in cPanel Node.js app settings:
   - NODE_ENV=production
   - PORT=3000
   - MONGODB_URI=your_mongodb_connection_string
   - NEXTAUTH_URL=https://bestlyfefashion.com

## Next Steps:
1. Upload all files to public_html
2. Create Node.js app in cPanel with:
   - Node.js version: 22.17.0
   - Startup file: server.js
   - Application root: public_html
3. Install dependencies: npm install --production
4. Start application: npm start or pm2 start ecosystem.config.js
