// Health Check Script for BanaHosting Deployment
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔍 BanaHosting Health Check Starting...');
console.log('=====================================');

// Check Node.js version
console.log(`📋 Node.js Version: ${process.version}`);
console.log(`📋 Platform: ${process.platform}`);
console.log(`📋 Architecture: ${process.arch}`);

// Check environment variables
console.log('\n🌍 Environment Variables:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
console.log(`PORT: ${process.env.PORT || 'not set'}`);
console.log(`MONGODB_URI: ${process.env.MONGODB_URI ? 'set' : 'not set'}`);

// Check required files
console.log('\n📁 File System Check:');
const requiredFiles = [
    'package.json',
    'server.js',
    'next.config.js',
    '.next',
    'public'
];

requiredFiles.forEach(file => {
    const exists = fs.existsSync(path.join(__dirname, file));
    console.log(`${exists ? '✅' : '❌'} ${file}: ${exists ? 'exists' : 'missing'}`);
});

// Check package.json
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log(`\n📦 Package Info:`);
    console.log(`Name: ${packageJson.name}`);
    console.log(`Version: ${packageJson.version}`);
    console.log(`Scripts available: ${Object.keys(packageJson.scripts).join(', ')}`);
} catch (error) {
    console.log('❌ Error reading package.json:', error.message);
}

// Test MongoDB connection if URI is available
if (process.env.MONGODB_URI) {
    console.log('\n🗄️  Testing MongoDB Connection...');
    const { MongoClient } = require('mongodb');
    
    MongoClient.connect(process.env.MONGODB_URI)
        .then(client => {
            console.log('✅ MongoDB connection successful');
            client.close();
        })
        .catch(error => {
            console.log('❌ MongoDB connection failed:', error.message);
        });
} else {
    console.log('\n🗄️  MongoDB URI not set - skipping connection test');
}

// Test basic HTTP server
console.log('\n🌐 Testing HTTP Server...');
const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Health check OK');
});

const port = process.env.PORT || 3000;
server.listen(port, '0.0.0.0', () => {
    console.log(`✅ HTTP Server listening on port ${port}`);
    console.log(`🔗 Test URL: http://localhost:${port}`);
    
    // Test the server
    const testReq = http.get(`http://localhost:${port}`, (res) => {
        console.log(`✅ Server response: ${res.statusCode}`);
        server.close();
        console.log('\n🎉 Health check completed!');
    });
    
    testReq.on('error', (error) => {
        console.log('❌ Server test failed:', error.message);
        server.close();
    });
});

server.on('error', (error) => {
    console.log('❌ Server failed to start:', error.message);
    if (error.code === 'EADDRINUSE') {
        console.log(`Port ${port} is already in use. Try a different port.`);
    }
});
