#!/bin/bash

# BanaHosting Deployment Script for Best Lyfe Fashion
echo "🚀 Starting deployment to BanaHosting..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Make sure you're in the project root."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Install dependencies
echo "📦 Installing dependencies..."
npm install --production

# Build the project
echo "🔨 Building the project..."
npm run build

# Check if build was successful
if [ ! -d ".next" ]; then
    echo "❌ Error: Build failed. .next directory not found."
    exit 1
fi

echo "✅ Build completed successfully!"

# Create a simple startup script for BanaHosting
cat > start.sh << 'EOF'
#!/bin/bash
export NODE_ENV=production
export PORT=3000
node server.js
EOF

chmod +x start.sh

echo "📋 Deployment checklist:"
echo "1. ✅ Dependencies installed"
echo "2. ✅ Project built"
echo "3. ✅ Startup script created"
echo ""
echo "📁 Files to upload to BanaHosting:"
echo "   - .next/ (entire folder)"
echo "   - public/ (entire folder)"
echo "   - node_modules/ (entire folder)"
echo "   - package.json"
echo "   - package-lock.json"
echo "   - server.js"
echo "   - next.config.js"
echo "   - ecosystem.config.js"
echo "   - start.sh"
echo "   - .env.local (with your environment variables)"
echo ""
echo "⚙️  BanaHosting Configuration:"
echo "   - Application startup file: server.js"
echo "   - Node.js version: Latest LTS"
echo "   - Port: 3000"
echo ""
echo "🌍 Environment Variables to set in cPanel:"
echo "   NODE_ENV=production"
echo "   MONGODB_URI=your_mongodb_connection_string"
echo "   NEXTAUTH_URL=https://bestlyfefashion.com"
echo ""
echo "🚀 Deployment ready! Upload the files and configure your Node.js app in cPanel."
