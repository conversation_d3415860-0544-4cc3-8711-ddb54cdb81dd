{"name": "bestlyfefashion", "version": "0.1.0", "private": true, "scripts": {"dev": "nodemon --exec \"next dev\"", "dev:next": "next dev", "dev:watch": "nodemon --watch models --watch lib --watch app/api --exec \"npm run dev:next\"", "build": "next build", "export": "next build", "start": "next start", "lint": "next lint", "server": "node server.js", "health-check": "node health-check.js", "test-production": "node test-production.js", "deploy-prep": "bash deploy-banahosting.sh"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dotenv": "^17.2.1", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "mongodb": "^6.17.0", "mongoose": "^8.16.3", "next": "14.0.4", "next-themes": "^0.4.6", "react": "^18", "react-day-picker": "^9.8.0", "react-dom": "^18", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "nodemon": "^3.1.10", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}