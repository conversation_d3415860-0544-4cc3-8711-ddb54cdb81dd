'use client'

import { useState, useEffect } from 'react'

interface SzwegoModalProps {
  isOpen: boolean
  onClose: () => void
  redirectUrl: string
}

export default function SzwegoModal({ isOpen, onClose, redirectUrl }: SzwegoModalProps) {
  const [countdown, setCountdown] = useState(5)

  useEffect(() => {
    if (!isOpen) return

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Redirect when countdown reaches 0
          window.open(redirectUrl, '_blank')
          onClose()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isOpen, redirectUrl, onClose])

  useEffect(() => {
    if (isOpen) {
      setCountdown(5) // Reset countdown when modal opens
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-8 max-w-md w-full text-center shadow-2xl">
        {/* Icon */}
        <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>

        {/* Title */}
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Navigation Tip
        </h2>

        {/* Message */}
        <p className="text-lg text-gray-700 mb-6 leading-relaxed">
          Click the filter button to view Better
        </p>

        {/* Countdown */}
        <div className="flex items-center justify-center gap-2 mb-6">
          <span className="text-gray-600">Redirecting in</span>
          <div className="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center font-bold">
            {countdown}
          </div>
          <span className="text-gray-600">seconds</span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-orange-600 h-2 rounded-full transition-all duration-1000 ease-linear"
            style={{ width: `${((5 - countdown) / 5) * 100}%` }}
          ></div>
        </div>

        {/* Skip Button */}
        <button
          onClick={() => {
            window.open(redirectUrl, '_blank')
            onClose()
          }}
          className="text-orange-600 hover:text-orange-700 font-medium transition-colors"
        >
          Skip and go now →
        </button>
      </div>
    </div>
  )
}
