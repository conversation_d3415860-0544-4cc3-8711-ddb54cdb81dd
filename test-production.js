// Test Production Build Locally
const { spawn } = require('child_process');
const http = require('http');

console.log('🧪 Testing Production Build Locally...');
console.log('=====================================');

// Set production environment
process.env.NODE_ENV = 'production';
process.env.PORT = '3001'; // Use different port to avoid conflicts

console.log('🔨 Building project...');

// Build the project
const buildProcess = spawn('npm', ['run', 'build'], {
    stdio: 'inherit',
    shell: true
});

buildProcess.on('close', (code) => {
    if (code !== 0) {
        console.log('❌ Build failed with code:', code);
        process.exit(1);
    }
    
    console.log('✅ Build completed successfully!');
    console.log('🚀 Starting production server...');
    
    // Start the production server
    const serverProcess = spawn('node', ['server.js'], {
        stdio: 'inherit',
        shell: true,
        env: { ...process.env }
    });
    
    // Wait a bit for server to start
    setTimeout(() => {
        console.log('🔍 Testing server response...');
        
        const testReq = http.get('http://localhost:3001', (res) => {
            console.log(`✅ Server responded with status: ${res.statusCode}`);
            
            if (res.statusCode === 200) {
                console.log('🎉 Production test successful!');
                console.log('📋 Your app is ready for deployment to BanaHosting.');
            } else {
                console.log('⚠️  Server responded but with non-200 status.');
            }
            
            serverProcess.kill();
            process.exit(0);
        });
        
        testReq.on('error', (error) => {
            console.log('❌ Failed to connect to server:', error.message);
            serverProcess.kill();
            process.exit(1);
        });
        
    }, 5000); // Wait 5 seconds for server to start
    
    serverProcess.on('error', (error) => {
        console.log('❌ Failed to start server:', error.message);
        process.exit(1);
    });
});

buildProcess.on('error', (error) => {
    console.log('❌ Failed to start build process:', error.message);
    process.exit(1);
});
